<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="商品详情页面测试"
        android:textSize="18sp"
        android:textStyle="bold" />

    <Button
        android:id="@+id/btn_test_product_detail"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_add_to_cart_button"
        android:text="打开商品详情"
        android:textColor="@android:color/white"
        android:textSize="16sp" />

</LinearLayout>
